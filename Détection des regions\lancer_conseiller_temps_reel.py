#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 LANCEUR CONSEILLER POKER TEMPS RÉEL COMPLET
==============================================

Script principal qui lance le conseiller poker avec détection automatique
des joueurs et conseils en temps réel basés sur leurs actions.
"""

import sys
import os
import time
import argparse
from typing import Optional

def check_dependencies():
    """Vérifie les dépendances nécessaires"""
    print("🔍 Vérification des dépendances...")
    
    required_modules = [
        ('mss', 'Capture d\'écran'),
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('tkinter', 'Interface graphique')
    ]
    
    missing = []
    
    for module, description in required_modules:
        try:
            __import__(module)
            print(f"✅ {description} ({module})")
        except ImportError:
            print(f"❌ {description} ({module}) - MANQUANT")
            missing.append(module)
    
    # Vérifier les modules optionnels
    optional_modules = [
        ('easyocr', 'EasyOCR'),
        ('paddleocr', 'PaddleOCR')
    ]
    
    for module, description in optional_modules:
        try:
            __import__(module)
            print(f"✅ {description} ({module}) - Optionnel")
        except ImportError:
            print(f"⚠️ {description} ({module}) - Optionnel, non disponible")
    
    if missing:
        print(f"\n❌ Modules manquants: {', '.join(missing)}")
        print("💡 Installez-les avec: pip install " + " ".join(missing))
        return False
    
    print("✅ Toutes les dépendances requises sont disponibles")
    return True

def check_config_files():
    """Vérifie la présence des fichiers de configuration"""
    print("\n🔍 Vérification des fichiers de configuration...")
    
    config_paths = [
        r"C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json",
        "calibration_config.json",
        "../Calibration/config/poker_advisor_config.json"
    ]
    
    config_found = None
    
    for path in config_paths:
        if os.path.exists(path):
            config_found = path
            print(f"✅ Configuration trouvée: {path}")
            break
        else:
            print(f"⚠️ Configuration non trouvée: {path}")
    
    if not config_found:
        print("❌ Aucun fichier de configuration trouvé")
        print("💡 Assurez-vous d'avoir calibré votre application poker")
        return None
    
    return config_found

def launch_gui_mode(config_path: Optional[str] = None):
    """Lance le mode interface graphique"""
    print("\n🎯 Lancement du mode interface graphique...")
    
    try:
        from realtime_advisor_gui import RealtimeAdvisorGUI
        
        print("✅ Interface graphique chargée")
        app = RealtimeAdvisorGUI()
        
        print("🚀 Démarrage de l'interface...")
        app.run()
        
    except ImportError as e:
        print(f"❌ Erreur import interface: {e}")
        print("💡 Vérifiez que realtime_advisor_gui.py est présent")
        return False
    except Exception as e:
        print(f"❌ Erreur lancement interface: {e}")
        return False
    
    return True

def launch_console_mode(config_path: Optional[str] = None):
    """Lance le mode console"""
    print("\n💻 Lancement du mode console...")
    
    try:
        from integration_detection_existante import start_enhanced_monitoring
        
        print("✅ Système de détection chargé")
        advisor = start_enhanced_monitoring(config_path)
        
        print("🚀 Surveillance démarrée")
        print("📊 Conseils en temps réel activés")
        print("💡 Appuyez sur Ctrl+C pour arrêter")
        
        # Boucle principale
        try:
            while True:
                time.sleep(5)
                
                # Afficher un résumé périodique
                status = advisor.get_status_report()
                if status and "error" not in status:
                    table = status.get("table_summary", {})
                    confidence = status.get("confidence_metrics", {})
                    
                    print(f"\n📊 Status: {table.get('player_count', 0)} joueurs, "
                          f"Confiance: {confidence.get('overall', 0)*100:.1f}%")
                    
                    # Obtenir un conseil si des joueurs sont détectés
                    if table.get('player_count', 0) > 0:
                        advice = advisor.get_realtime_advice()
                        if advice and "error" not in advice:
                            action = advice.get("final_recommendation", "N/A")
                            print(f"💡 Conseil: {action}")
                
        except KeyboardInterrupt:
            print("\n⏹️ Arrêt demandé par l'utilisateur")
        
        # Arrêter proprement
        advisor.stop()
        print("✅ Surveillance arrêtée")
        
    except ImportError as e:
        print(f"❌ Erreur import système: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur mode console: {e}")
        return False
    
    return True

def launch_test_mode(config_path: Optional[str] = None):
    """Lance le mode test"""
    print("\n🧪 Lancement du mode test...")
    
    try:
        from auto_player_detection import create_auto_detector
        
        print("✅ Détecteur automatique chargé")
        detector = create_auto_detector(config_path)
        
        # Test de détection
        print("🔄 Test de détection des joueurs...")
        detector.force_player_scan()
        
        time.sleep(2)
        
        # Afficher le résumé
        summary = detector.get_table_summary()
        print(f"📊 Résultats du test:")
        print(f"  • Joueurs détectés: {len(summary.get('active_players', []))}")
        print(f"  • Joueurs: {', '.join(summary.get('active_players', []))}")
        print(f"  • Pot: {summary.get('pot', 0)}")
        print(f"  • Mes jetons: {summary.get('my_chips', 0)}")
        print(f"  • Mes cartes: {' '.join(summary.get('my_cards', []))}")
        print(f"  • Board: {' '.join(summary.get('board_cards', []))}")
        
        print("✅ Test terminé")
        
    except ImportError as e:
        print(f"❌ Erreur import test: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur mode test: {e}")
        return False
    
    return True

def main():
    """Fonction principale"""
    print("🎯 CONSEILLER POKER TEMPS RÉEL")
    print("=" * 50)
    print("Détection automatique des joueurs et conseils en temps réel")
    print("=" * 50)
    
    # Parser les arguments
    parser = argparse.ArgumentParser(description="Conseiller Poker Temps Réel")
    parser.add_argument("--mode", choices=["gui", "console", "test"], default="gui",
                       help="Mode de lancement (défaut: gui)")
    parser.add_argument("--config", type=str, help="Chemin vers le fichier de configuration")
    parser.add_argument("--no-check", action="store_true", help="Ignorer les vérifications")
    
    args = parser.parse_args()
    
    # Vérifications préliminaires
    if not args.no_check:
        if not check_dependencies():
            print("\n❌ Vérifications échouées")
            return 1
        
        config_path = args.config or check_config_files()
        if not config_path:
            print("\n❌ Configuration manquante")
            return 1
    else:
        config_path = args.config
        print("⚠️ Vérifications ignorées")
    
    # Lancement selon le mode
    success = False
    
    if args.mode == "gui":
        success = launch_gui_mode(config_path)
    elif args.mode == "console":
        success = launch_console_mode(config_path)
    elif args.mode == "test":
        success = launch_test_mode(config_path)
    
    if success:
        print("\n✅ Conseiller poker terminé avec succès")
        return 0
    else:
        print("\n❌ Erreur lors de l'exécution")
        return 1

def quick_start():
    """Démarrage rapide avec interface graphique"""
    print("🚀 DÉMARRAGE RAPIDE - Interface Graphique")
    print("=" * 50)
    
    # Vérifications minimales
    try:
        import tkinter
        import mss
        import cv2
        import numpy
    except ImportError as e:
        print(f"❌ Module manquant: {e}")
        print("💡 Installez avec: pip install mss opencv-python numpy")
        return 1
    
    # Lancer directement l'interface
    return launch_gui_mode()

if __name__ == "__main__":
    # Si lancé sans arguments, utiliser le démarrage rapide
    if len(sys.argv) == 1:
        exit_code = quick_start()
    else:
        exit_code = main()
    
    sys.exit(exit_code)

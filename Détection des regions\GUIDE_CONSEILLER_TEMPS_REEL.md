# 🎯 GUIDE CONSEILLER POKER TEMPS RÉEL

## 📋 Description

Le **Conseiller Poker Temps Réel** capture automatiquement les pseudos des joueurs à votre table et vous donne des conseils personnalisés en temps réel basés sur leurs actions et leurs profils.

## 🚀 Fonctionnalités

### 🎭 **Détection Automatique des Joueurs**
- **Capture automatique** des pseudos via OCR
- **Reconnaissance** des joueurs dans votre base de données
- **Mise à jour temps réel** de la composition de la table

### 💡 **Conseils Personnalisés en Temps Réel**
- **Analyse instantanée** des actions adversaires (bet, raise, all-in)
- **Recommandations spécifiques** selon le profil de chaque joueur
- **Alertes** sur les joueurs dangereux ou les situations critiques

### 🔍 **Détection des Actions**
- **Montants des mises** avec détection des all-ins (rouge)
- **Types d'actions** (fold, call, bet, raise, check, all-in)
- **Historique** des actions récentes pour analyse

### 🧠 **Intelligence Adaptative**
- **Profils détaillés** : VPIP, PFR, Facteur d'agressivité
- **Styles de jeu** : TAG, LAG, Nit, Calling Station, Maniac
- **Recommandations contextuelles** selon l'action et le joueur

## 📁 Fichiers du Système

```
📦 Conseiller Temps Réel
├── 🎯 auto_player_detection.py              # Détection automatique
├── 🖥️ realtime_advisor_gui.py               # Interface graphique
├── 🔗 integration_detection_existante.py    # Intégration système existant
├── 🚀 lancer_conseiller_temps_reel.py       # Lanceur principal
├── ⚡ lancer_conseiller_temps_reel.bat      # Lancement en un clic
├── 📄 GUIDE_CONSEILLER_TEMPS_REEL.md        # Ce guide
└── 🎯 poker_tracker_intelligent.py          # Base de données joueurs
```

## 🛠️ Installation et Configuration

### 1. **Prérequis**
```bash
# Modules Python requis (normalement déjà installés)
pip install mss opencv-python numpy easyocr

# Modules optionnels pour de meilleures performances
pip install paddleocr
```

### 2. **Vérification de la Configuration**
Assurez-vous que vos régions sont calibrées dans :
```
C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json
```

**Régions nécessaires :**
- `pseudo_joueur1` à `pseudo_joueur7` : Pseudos des adversaires
- `mise_joueur1` à `mise_joueur7` : Mises des adversaires
- `carte_1m`, `carte_2m` : Vos cartes en main
- `card_1` à `card_5` : Cartes du board
- `pot_total` : Montant du pot
- `mes_jetons` : Vos jetons

## 🎮 Utilisation

### **Lancement Rapide**
```bash
# Double-clic sur :
lancer_conseiller_temps_reel.bat

# Ou manuellement :
python lancer_conseiller_temps_reel.py
```

### **Interface Graphique**

#### **Section Contrôle (Gauche)**
- **🚀 Démarrer** : Lance la surveillance automatique
- **⏹️ Arrêter** : Arrête la surveillance
- **État de la table** : Affiche joueurs, pot, jetons, cartes détectés
- **Actions manuelles** : Boutons pour forcer les mises à jour

#### **Section Conseils (Droite)**
- **Zone de conseils** : Affiche les recommandations en temps réel
- **Timestamp** : Horodatage de chaque conseil
- **Sauvegarde** : Exporter les conseils vers un fichier

### **Modes de Lancement**

#### **Mode Interface Graphique (Recommandé)**
```bash
python lancer_conseiller_temps_reel.py --mode gui
```

#### **Mode Console**
```bash
python lancer_conseiller_temps_reel.py --mode console
```

#### **Mode Test**
```bash
python lancer_conseiller_temps_reel.py --mode test
```

## 🎯 Exemples de Conseils en Temps Réel

### **Exemple 1 : All-in d'un joueur agressif**
```
[14:32:15] 🎯 CONSEIL TEMPS RÉEL pour Badboy44700
Action: all-in 15000
Style: Loose-Aggressive (confiance: 85%)
Stats: VPIP 35.0% | PFR 28.0% | AF 3.2
⚠️ ATTENTION: Joueur agressif en all-in - main forte probable!
💡 Recommandations contre ce joueur:
  • 🛡️ Jouez plus tight contre lui
  • 🎭 Piégez avec vos nuts
```

### **Exemple 2 : Grosse mise d'un joueur serré**
```
[14:35:42] 🎯 CONSEIL TEMPS RÉEL pour ludogrnx
Action: bet 8000
Style: Tight-Passive (confiance: 92%)
Stats: VPIP 12.0% | PFR 8.0% | AF 1.2
⚠️ Grosse mise d'un joueur serré - main très forte!
💡 Recommandations contre ce joueur:
  • 🎯 Volez ses blinds fréquemment
  • 💰 Value bet thin contre lui
```

### **Exemple 3 : Conseil global de table**
```
[14:38:20] 💡 CONSEIL ACTUEL
========================================
🎯 Action recommandée: fold
👥 Joueurs: Prelyna, Badboy44700, ludogrnx
💰 Pot: 12,500
🎯 Mes jetons: 45,000
📋 Actions récentes:
  • Prelyna: bet 3000
  • Badboy44700: raise 8000
  • ludogrnx: fold 0
🔧 Ajustements:
  • Contre LAG avec grosse relance: être plus prudent
```

## ⚙️ Configuration Avancée

### **Intervalles de Détection**
```python
# Dans auto_player_detection.py
self.detection_interval = 2.0      # Détection toutes les 2 secondes
self.player_scan_interval = 10.0   # Scan pseudos toutes les 10 secondes
```

### **Seuils de Détection**
```python
# Seuil pour détecter les all-ins (rouge)
red_ratio > 0.1  # 10% de pixels rouges

# Validation des pseudos
len(cleaned) >= 3 and len(cleaned) <= 20  # Entre 3 et 20 caractères
```

### **Intégration avec Votre Système Existant**
Le système s'intègre automatiquement avec :
- `detector_cuda_optimized.py` : Détection CUDA
- `multi_ocr_detector.py` : Multi-OCR
- Votre base de données de joueurs existante

## 🔧 Dépannage

### **Problèmes Courants**

#### **"Aucun joueur détecté"**
```
Solutions :
1. Vérifiez la calibration des régions pseudo_joueur1-7
2. Assurez-vous que Winamax est visible à l'écran
3. Testez avec le mode test : --mode test
```

#### **"Erreur de détection OCR"**
```
Solutions :
1. Installez EasyOCR : pip install easyocr
2. Vérifiez que les pseudos sont visibles et contrastés
3. Ajustez les paramètres de prétraitement d'image
```

#### **"Conseils non pertinents"**
```
Solutions :
1. Mettez à jour la base de données : bouton "Scanner joueurs"
2. Jouez plus de mains pour améliorer les profils
3. Vérifiez que les actions sont correctement détectées
```

#### **"Interface lente"**
```
Solutions :
1. Réduisez l'intervalle de détection
2. Fermez les autres applications
3. Utilisez le mode console pour de meilleures performances
```

### **Logs et Debug**
```python
# Activer les logs détaillés
import logging
logging.basicConfig(level=logging.DEBUG)

# Tester un composant spécifique
from auto_player_detection import create_auto_detector
detector = create_auto_detector()
detector.force_player_scan()
```

## 📊 Métriques de Confiance

Le système calcule automatiquement la confiance de ses conseils :

- **Joueurs détectés** : +20% par joueur (max 80%)
- **Cartes détectées** : +10% par carte (max 50%)
- **Montants détectés** : +15% si pot et jetons détectés
- **Actions récentes** : +5% par action (max 20%)

**Confiance globale** : Combinaison de tous les facteurs (max 100%)

## 🎯 Conseils d'Utilisation

### **Pour Maximiser l'Efficacité :**

1. **Calibrez précisément** toutes les régions nécessaires
2. **Laissez le système apprendre** en jouant plusieurs sessions
3. **Observez les conseils** et adaptez votre jeu en conséquence
4. **Mettez à jour régulièrement** la base de données des joueurs
5. **Utilisez les alertes** pour identifier les situations critiques

### **Stratégies Recommandées :**

- **Contre les joueurs agressifs** : Soyez plus sélectif et piégez
- **Contre les joueurs passifs** : Value bet large et volez les blinds
- **Contre les joueurs inconnus** : Observez attentivement leurs premières actions
- **En cas d'all-in** : Considérez le profil du joueur avant de décider

## 🚨 Alertes Importantes

### **Alertes Automatiques :**
- 🔥 **Joueur très agressif en all-in** : Main forte probable
- 🚨 **Joueur passif en all-in** : NUTS probable
- ⚠️ **Grosse mise d'un joueur serré** : Main très forte
- 💡 **Grosse mise d'un LAG** : Peut être un bluff

### **Indicateurs Visuels :**
- **Vert** : Joueur Tight-Passive (Nit)
- **Bleu** : Joueur Tight-Aggressive (TAG)
- **Orange** : Joueur Loose-Passive (Calling Station)
- **Rouge** : Joueur Loose-Aggressive (LAG)
- **Violet** : Joueur Maniac

---

**🎉 Avec ce système, vous avez un avantage considérable en temps réel ! Chaque action de vos adversaires est analysée et vous recevez des conseils personnalisés instantanément.**

## 📞 Support

Pour toute question :
1. Lancez d'abord le mode test : `--mode test`
2. Vérifiez les logs dans la console
3. Consultez ce guide pour les solutions courantes

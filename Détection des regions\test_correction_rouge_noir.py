#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de la correction rouge/noir pour les cartes
===============================================

Ce script teste spécifiquement la correction du problème où
le rouge des cartes était détecté comme noir.

Auteur: Augment Agent
Date: 2024
"""

import os
import sys
import cv2
import numpy as np

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_card_images():
    """Créer des images de test pour les cartes rouge et noir"""
    test_images = {}
    
    # 1. Carte rouge pure (cœur)
    red_card = np.zeros((50, 40, 3), dtype=np.uint8)
    # Fond blanc
    red_card[:, :] = [255, 255, 255]
    # Symbole rouge au centre
    cv2.rectangle(red_card, (15, 20), (25, 30), (0, 0, 200), -1)  # Rouge BGR
    # Texte rouge
    cv2.putText(red_card, "A", (5, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 200), 1)
    test_images["Carte rouge (cœur)"] = red_card
    
    # 2. Carte rouge foncé (cœur sombre)
    dark_red_card = np.zeros((50, 40, 3), dtype=np.uint8)
    dark_red_card[:, :] = [240, 240, 240]  # Fond gris clair
    cv2.rectangle(dark_red_card, (15, 20), (25, 30), (0, 0, 150), -1)  # Rouge foncé
    cv2.putText(dark_red_card, "K", (5, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 150), 1)
    test_images["Carte rouge foncé"] = dark_red_card
    
    # 3. Carte noire pure (pique)
    black_card = np.zeros((50, 40, 3), dtype=np.uint8)
    black_card[:, :] = [255, 255, 255]  # Fond blanc
    cv2.rectangle(black_card, (15, 20), (25, 30), (0, 0, 0), -1)  # Noir
    cv2.putText(black_card, "A", (5, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    test_images["Carte noire (pique)"] = black_card
    
    # 4. Carte avec rouge et noir mélangés
    mixed_card = np.zeros((50, 40, 3), dtype=np.uint8)
    mixed_card[:, :] = [255, 255, 255]  # Fond blanc
    cv2.rectangle(mixed_card, (10, 15), (20, 25), (0, 0, 180), -1)  # Rouge
    cv2.rectangle(mixed_card, (20, 25), (30, 35), (0, 0, 0), -1)  # Noir
    cv2.putText(mixed_card, "Q", (5, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 180), 1)
    test_images["Carte mixte rouge/noir"] = mixed_card
    
    # 5. Carte carreau (bleu)
    blue_card = np.zeros((50, 40, 3), dtype=np.uint8)
    blue_card[:, :] = [255, 255, 255]  # Fond blanc
    cv2.rectangle(blue_card, (15, 20), (25, 30), (200, 0, 0), -1)  # Bleu BGR
    cv2.putText(blue_card, "J", (5, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 0, 0), 1)
    test_images["Carte bleue (carreau)"] = blue_card
    
    # 6. Carte trèfle (vert)
    green_card = np.zeros((50, 40, 3), dtype=np.uint8)
    green_card[:, :] = [255, 255, 255]  # Fond blanc
    cv2.rectangle(green_card, (15, 20), (25, 30), (0, 150, 0), -1)  # Vert BGR
    cv2.putText(green_card, "10", (2, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 150, 0), 1)
    test_images["Carte verte (trèfle)"] = green_card
    
    return test_images

def test_detection_couleurs_cartes():
    """Test de la détection des couleurs pour les cartes"""
    print("🃏 TEST DÉTECTION COULEURS CARTES")
    print("="*60)
    
    try:
        from detector import Detector
        detector = Detector()
        
        test_images = create_test_card_images()
        results = {}
        
        for name, image in test_images.items():
            print(f"\n🧪 Test: {name}")
            print("-" * 40)
            
            # Détecter les couleurs
            colors = detector.detect_colors(image)
            results[name] = colors
            
            # Interprétation
            if 'red' in colors:
                interpretation = "🔴 ROUGE détecté (cœur/carreau rouge)"
            elif 'black' in colors:
                interpretation = "⚫ NOIR détecté (pique/trèfle)"
            elif 'blue' in colors:
                interpretation = "🔵 BLEU détecté (carreau)"
            elif 'green' in colors:
                interpretation = "🟢 VERT détecté (trèfle)"
            elif 'white' in colors:
                interpretation = "⚪ BLANC détecté (fond de carte)"
            else:
                interpretation = f"❓ COULEURS: {colors}"
            
            print(f"   Résultat: {colors}")
            print(f"   Interprétation: {interpretation}")
        
        # Vérifications spécifiques
        print(f"\n🔍 VÉRIFICATIONS SPÉCIFIQUES:")
        
        # 1. Carte rouge doit être détectée comme rouge
        red_result = results.get("Carte rouge (cœur)", [])
        if 'red' in red_result and 'black' not in red_result:
            print("✅ Carte rouge correctement détectée (pas de confusion avec noir)")
        else:
            print(f"❌ Problème carte rouge: {red_result}")
        
        # 2. Carte rouge foncé doit être détectée comme rouge
        dark_red_result = results.get("Carte rouge foncé", [])
        if 'red' in dark_red_result and 'black' not in dark_red_result:
            print("✅ Carte rouge foncé correctement détectée")
        else:
            print(f"❌ Problème carte rouge foncé: {dark_red_result}")
        
        # 3. Carte noire doit être détectée comme noire
        black_result = results.get("Carte noire (pique)", [])
        if 'black' in black_result and 'red' not in black_result:
            print("✅ Carte noire correctement détectée")
        else:
            print(f"❌ Problème carte noire: {black_result}")
        
        # 4. Carte mixte doit privilégier le rouge
        mixed_result = results.get("Carte mixte rouge/noir", [])
        if 'red' in mixed_result:
            print("✅ Carte mixte privilégie le rouge (correct)")
        else:
            print(f"❌ Carte mixte ne privilégie pas le rouge: {mixed_result}")
        
        # 5. Carte bleue doit être détectée comme bleue
        blue_result = results.get("Carte bleue (carreau)", [])
        if 'blue' in blue_result:
            print("✅ Carte bleue correctement détectée")
        else:
            print(f"⚠️ Carte bleue: {blue_result}")
        
        # 6. Carte verte doit être détectée comme verte
        green_result = results.get("Carte verte (trèfle)", [])
        if 'green' in green_result:
            print("✅ Carte verte correctement détectée")
        else:
            print(f"⚠️ Carte verte: {green_result}")
        
        print("\n✅ Test détection couleurs cartes terminé")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plages_hsv():
    """Test des nouvelles plages HSV"""
    print("\n🎨 TEST PLAGES HSV CORRIGÉES")
    print("="*60)
    
    try:
        from detector import Detector
        detector = Detector()
        
        # Afficher les nouvelles plages
        print("📊 Nouvelles plages de couleurs:")
        for color_name, ranges in detector.color_ranges.items():
            print(f"   {color_name}: {ranges}")
        
        # Vérifier que les plages sont correctes
        red_ranges = detector.color_ranges.get('red', [])
        black_ranges = detector.color_ranges.get('black', [])
        
        print(f"\n🔴 Plages ROUGE: {red_ranges}")
        print(f"⚫ Plages NOIR: {black_ranges}")
        
        # Vérifier que le noir est plus restrictif
        if black_ranges:
            black_upper_v = black_ranges[0]['upper'][2]  # Valeur V (luminosité)
            print(f"   Luminosité max noir: {black_upper_v}")
            if black_upper_v <= 40:
                print("✅ Plage noir restrictive (luminosité ≤ 40)")
            else:
                print(f"⚠️ Plage noir peut-être trop large (luminosité = {black_upper_v})")
        
        # Vérifier que le rouge est plus permissif
        if red_ranges:
            red_lower_s = red_ranges[0]['lower'][1]  # Saturation S
            print(f"   Saturation min rouge: {red_lower_s}")
            if red_lower_s <= 80:
                print("✅ Plage rouge permissive (saturation ≥ 80)")
            else:
                print(f"⚠️ Plage rouge peut-être trop restrictive (saturation = {red_lower_s})")
        
        print("\n✅ Test plages HSV terminé")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test plages: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 TEST CORRECTION ROUGE/NOIR POUR CARTES")
    print("="*60)
    print("Test pour résoudre le problème :")
    print("- Rouge des cartes détecté comme noir")
    print("- Amélioration de la logique de priorité")
    
    # Exécuter tous les tests
    tests_results = []
    
    tests_results.append(("Détection couleurs cartes", test_detection_couleurs_cartes()))
    tests_results.append(("Plages HSV corrigées", test_plages_hsv()))
    
    # Résumé des résultats
    print("\n" + "="*60)
    print("📊 RÉSUMÉ DES TESTS CORRECTION ROUGE/NOIR")
    print("="*60)
    
    passed = 0
    total = len(tests_results)
    
    for test_name, result in tests_results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Correction rouge/noir opérationnelle!")
        print("\n💡 Les améliorations apportées :")
        print("   ✅ Plages HSV corrigées (rouge élargi, noir restrictif)")
        print("   ✅ Seuils adaptés (rouge: 2%, noir: 5%)")
        print("   ✅ Logique de priorité (rouge privilégié)")
        print("   ✅ Résolution de conflit simplifiée")
        print("\n🚀 Le rouge des cartes ne devrait plus être confondu avec le noir !")
    else:
        print("⚠️ Certaines corrections ont échoué.")
        print("🔧 Vérifiez les logs d'erreur ci-dessus.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*60}")
    if success:
        print("✅ Tests de correction rouge/noir terminés avec succès")
        print("🎯 La détection des couleurs de cartes est corrigée")
    else:
        print("❌ Certaines corrections ont échoué")
        print("🔧 Vérifiez les messages d'erreur ci-dessus")
    print("="*60)
    
    sys.exit(0 if success else 1)

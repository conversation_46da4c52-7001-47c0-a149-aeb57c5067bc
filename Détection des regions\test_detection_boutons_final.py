#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test final de la détection des boutons avec analyse détaillée
============================================================

Ce script teste la nouvelle détection des boutons avec :
1. Analyse détaillée des couleurs par plages
2. Logs complets pour debugging
3. Tests de cas problématiques (orange/noir confusion)

Auteur: Augment Agent
Date: 2024
"""

import os
import sys
import cv2
import numpy as np

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_images():
    """Créer des images de test pour différents scénarios"""
    test_images = {}
    
    # 1. Orange pur (tapis)
    orange_img = np.zeros((40, 40, 3), dtype=np.uint8)
    # Créer un orange HSV spécifique puis convertir en BGR
    orange_hsv = np.array([[[16, 200, 200]]], dtype=np.uint8)
    orange_bgr = cv2.cvtColor(orange_hsv, cv2.COLOR_HSV2BGR)[0][0]
    orange_img[:, :] = orange_bgr
    test_images["Orange pur (tapis)"] = orange_img
    
    # 2. Blanc pur (bouton dealer)
    white_img = np.zeros((40, 40, 3), dtype=np.uint8)
    white_img[:, :] = [255, 255, 255]
    test_images["Blanc pur (bouton dealer)"] = white_img
    
    # 3. Noir pur (autre bouton)
    black_img = np.zeros((40, 40, 3), dtype=np.uint8)
    black_img[:, :] = [30, 30, 30]  # Noir grisâtre
    test_images["Noir/Gris (autre bouton)"] = black_img
    
    # 4. Rouge pur (ne devrait pas être confondu avec orange)
    red_img = np.zeros((40, 40, 3), dtype=np.uint8)
    red_img[:, :] = [0, 0, 255]  # Rouge pur
    test_images["Rouge pur"] = red_img
    
    # 5. Mélange orange/noir (cas problématique)
    mixed_orange_black = np.zeros((40, 40, 3), dtype=np.uint8)
    mixed_orange_black[:20, :] = orange_bgr  # Moitié orange
    mixed_orange_black[20:, :] = [30, 30, 30]  # Moitié noir
    test_images["Mélange orange/noir"] = mixed_orange_black
    
    # 6. Gris moyen (bouton gris)
    gray_img = np.zeros((40, 40, 3), dtype=np.uint8)
    gray_img[:, :] = [100, 100, 100]  # Gris moyen
    test_images["Gris moyen"] = gray_img
    
    # 7. Très sombre (presque noir)
    dark_img = np.zeros((40, 40, 3), dtype=np.uint8)
    dark_img[:, :] = [10, 10, 10]  # Très sombre
    test_images["Très sombre"] = dark_img
    
    # 8. Orange clair (variante)
    light_orange_img = np.zeros((40, 40, 3), dtype=np.uint8)
    light_orange_hsv = np.array([[[20, 150, 180]]], dtype=np.uint8)
    light_orange_bgr = cv2.cvtColor(light_orange_hsv, cv2.COLOR_HSV2BGR)[0][0]
    light_orange_img[:, :] = light_orange_bgr
    test_images["Orange clair"] = light_orange_img
    
    # 9. Blanc grisâtre (bouton dealer moins contrasté)
    gray_white_img = np.zeros((40, 40, 3), dtype=np.uint8)
    gray_white_img[:, :] = [200, 200, 200]  # Blanc grisâtre
    test_images["Blanc grisâtre"] = gray_white_img
    
    # 10. Image vide/transparente (aucune couleur)
    empty_img = np.zeros((40, 40, 3), dtype=np.uint8)
    empty_img[:, :] = [5, 5, 5]  # Presque noir
    test_images["Presque vide"] = empty_img
    
    return test_images

def test_detection_boutons_complete():
    """Test complet de la détection des boutons avec tous les cas"""
    print("🔘 TEST COMPLET DE DÉTECTION DES BOUTONS")
    print("="*60)
    
    try:
        from detector import Detector
        detector = Detector()
        
        # Créer les images de test
        test_images = create_test_images()
        
        results = {}
        
        for i, (name, image) in enumerate(test_images.items(), 1):
            print(f"\n🧪 Test {i}: {name}")
            print("-" * 40)
            
            # Analyser l'image
            colors = detector.detect_colors_button(image)
            results[name] = colors
            
            # Interpréter le résultat
            if not colors:
                interpretation = "❓ AUCUNE COULEUR (région vide ou indéterminée)"
            elif 'orange' in colors:
                interpretation = "🟠 PAS DE BOUTON (tapis orange visible)"
            elif 'white' in colors:
                interpretation = "⚪ BOUTON DEALER (blanc détecté)"
            elif 'black' in colors:
                interpretation = "⚫ AUTRE BOUTON (noir/gris détecté)"
            elif 'red' in colors:
                interpretation = "🔴 ROUGE (probablement pas un bouton)"
            else:
                interpretation = f"❓ COULEUR INATTENDUE: {colors}"
            
            print(f"   Résultat: {colors}")
            print(f"   Interprétation: {interpretation}")
        
        # Résumé des résultats
        print("\n" + "="*60)
        print("📊 RÉSUMÉ DES RÉSULTATS")
        print("="*60)
        
        for name, colors in results.items():
            status_icon = "🟠" if 'orange' in colors else "⚪" if 'white' in colors else "⚫" if 'black' in colors else "🔴" if 'red' in colors else "❓"
            print(f"{status_icon} {name}: {colors}")
        
        # Vérifications spécifiques
        print("\n🔍 VÉRIFICATIONS SPÉCIFIQUES:")
        
        # 1. Orange ne doit pas être confondu avec noir
        orange_result = results.get("Orange pur (tapis)", [])
        mixed_result = results.get("Mélange orange/noir", [])
        
        if 'orange' in orange_result and 'black' not in orange_result:
            print("✅ Orange pur correctement détecté (pas de confusion avec noir)")
        else:
            print(f"❌ Problème avec orange pur: {orange_result}")
        
        # 2. Rouge ne doit pas être confondu avec orange
        red_result = results.get("Rouge pur", [])
        if 'red' in red_result and 'orange' not in red_result:
            print("✅ Rouge correctement distingué de l'orange")
        else:
            print(f"❌ Problème avec rouge: {red_result}")
        
        # 3. Blanc doit être détecté
        white_result = results.get("Blanc pur (bouton dealer)", [])
        if 'white' in white_result:
            print("✅ Blanc correctement détecté")
        else:
            print(f"❌ Problème avec blanc: {white_result}")
        
        # 4. Cas vides doivent retourner liste vide ou couleur dominante faible
        empty_result = results.get("Presque vide", [])
        print(f"ℹ️ Région presque vide: {empty_result}")
        
        print("\n✅ Test complet de détection des boutons terminé")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_avec_vraie_capture():
    """Test avec une vraie capture d'écran si possible"""
    print("\n🖥️ TEST AVEC VRAIE CAPTURE D'ÉCRAN")
    print("="*60)
    
    try:
        from detector import Detector
        
        # Chercher la configuration
        config_path = os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json')
        if not os.path.exists(config_path):
            print("⚠️ Configuration non trouvée, test ignoré")
            return True
        
        detector = Detector(config_path=config_path)
        
        # Essayer de faire une capture d'écran
        try:
            import mss
            with mss.mss() as sct:
                # Capturer une zone pour test
                monitor = {"top": 200, "left": 200, "width": 400, "height": 300}
                screenshot = sct.grab(monitor)
                img_array = np.array(screenshot)
                img_bgr = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
                
                print("📸 Capture d'écran réalisée")
                
                # Extraire quelques régions boutons pour test
                regions = detector.extract_regions(img_bgr)
                
                button_regions = {name: region for name, region in regions.items() 
                                if name.startswith('bouton_') and region is not None and region.size > 0}
                
                if button_regions:
                    print(f"🔘 {len(button_regions)} régions boutons trouvées pour test")
                    
                    for name, region_img in list(button_regions.items())[:3]:  # Tester les 3 premières
                        print(f"\n🧪 Test région réelle: {name}")
                        colors = detector.detect_colors_button(region_img)
                        print(f"   Résultat: {colors}")
                else:
                    print("⚠️ Aucune région bouton trouvée dans la capture")
                
        except ImportError:
            print("⚠️ Module mss non disponible pour capture d'écran")
        except Exception as e:
            print(f"⚠️ Erreur lors de la capture: {e}")
        
        print("✅ Test avec vraie capture terminé")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test avec capture: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 TESTS FINAUX DE DÉTECTION DES BOUTONS")
    print("="*60)
    print("Tests pour résoudre les problèmes :")
    print("1. Orange confondu avec noir")
    print("2. Aucune couleur détectée")
    print("3. Amélioration de la robustesse")
    
    # Exécuter tous les tests
    tests_results = []
    
    tests_results.append(("Détection boutons complète", test_detection_boutons_complete()))
    tests_results.append(("Test avec vraie capture", test_avec_vraie_capture()))
    
    # Résumé des résultats
    print("\n" + "="*60)
    print("📊 RÉSUMÉ DES TESTS FINAUX")
    print("="*60)
    
    passed = 0
    total = len(tests_results)
    
    for test_name, result in tests_results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests finaux sont passés!")
        print("\n💡 La nouvelle détection des boutons devrait résoudre :")
        print("   ✅ Confusion orange/noir avec analyse par plages")
        print("   ✅ Aucune couleur détectée avec seuils adaptatifs")
        print("   ✅ Logs détaillés pour debugging")
        print("   ✅ Logique de décision simplifiée et robuste")
        print("\n🚀 Testez maintenant avec votre application poker !")
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez les logs ci-dessus.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*60}")
    if success:
        print("✅ Tests finaux terminés avec succès")
        print("🎯 La détection des boutons est maintenant robuste")
    else:
        print("❌ Certains tests ont échoué")
        print("🔧 Vérifiez les messages d'erreur ci-dessus")
    print("="*60)
    
    sys.exit(0 if success else 1)

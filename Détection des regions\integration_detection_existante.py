#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 INTÉGRATION AVEC SYSTÈME DE DÉTECTION EXISTANT
=================================================

Module qui intègre le détecteur automatique avec votre système
de détection de cartes existant pour une précision maximale.
"""

import os
import sys
import json
import numpy as np
from typing import Dict, List, Optional, Any
from auto_player_detection import AutoPlayerDetector

class EnhancedAutoDetector(AutoPlayerDetector):
    """Détecteur automatique amélioré avec intégration du système existant"""
    
    def __init__(self, config_path: str = None):
        super().__init__(config_path)
        
        # Importer vos modules de détection existants
        self.existing_detector = None
        self.multi_ocr = None
        
        self._init_existing_detectors()
        
        print("🔗 Détecteur amélioré avec intégration système existant")
    
    def _init_existing_detectors(self):
        """Initialise les détecteurs existants"""
        try:
            # Essayer d'importer votre détecteur CUDA optimisé
            try:
                from detector_cuda_optimized import CudaOptimizedDetector
                self.existing_detector = CudaOptimizedDetector()
                print("✅ Détecteur CUDA optimisé intégré")
            except Exception as e:
                print(f"⚠️ Détecteur CUDA non disponible: {e}")
            
            # Essayer d'importer votre multi-OCR
            try:
                from multi_ocr_detector import MultiOCRDetector
                self.multi_ocr = MultiOCRDetector()
                print("✅ Multi-OCR détecteur intégré")
            except Exception as e:
                print(f"⚠️ Multi-OCR non disponible: {e}")
                
        except Exception as e:
            print(f"⚠️ Erreur initialisation détecteurs existants: {e}")
    
    def _detect_card_value(self, card_region: np.ndarray) -> str:
        """Détecte la valeur d'une carte en utilisant votre système existant"""
        try:
            # Essayer d'abord avec votre détecteur CUDA optimisé
            if self.existing_detector:
                try:
                    result = self.existing_detector.detect_text_fast(card_region, is_hand_card=True)
                    if result and len(result) >= 2:
                        return result.strip()
                except Exception as e:
                    print(f"⚠️ Erreur détecteur CUDA: {e}")
            
            # Essayer avec votre multi-OCR
            if self.multi_ocr:
                try:
                    result = self.multi_ocr.detect_card_value(card_region, is_hand_card=True)
                    if result and len(result) >= 2:
                        return result.strip()
                except Exception as e:
                    print(f"⚠️ Erreur multi-OCR: {e}")
            
            # Fallback vers la méthode de base
            return super()._detect_card_value(card_region)
            
        except Exception as e:
            print(f"❌ Erreur détection carte: {e}")
            return ""
    
    def _detect_amount_in_region(self, region_img: np.ndarray) -> str:
        """Détecte un montant en utilisant votre système existant"""
        try:
            # Utiliser votre système de détection de montants si disponible
            if self.existing_detector:
                try:
                    # Adapter selon votre API existante
                    result = self.existing_detector.detect_text_fast(region_img, is_hand_card=False)
                    if result:
                        return result.strip()
                except Exception as e:
                    print(f"⚠️ Erreur détection montant existant: {e}")
            
            # Fallback vers la méthode de base
            return super()._detect_amount_in_region(region_img)
            
        except Exception as e:
            print(f"❌ Erreur détection montant: {e}")
            return ""
    
    def _detect_text_in_region(self, region_img: np.ndarray, is_player_name: bool = False) -> str:
        """Détecte le texte en utilisant votre système existant"""
        try:
            # Pour les pseudos, utiliser votre système optimisé
            if is_player_name and self.multi_ocr:
                try:
                    # Adapter selon votre API pour la détection de texte
                    result = self.multi_ocr.detect_with_easyocr([region_img])
                    if result and len(result) > 0:
                        # Prendre le premier résultat valide
                        for text in result:
                            if text and len(text) >= 3:
                                return text.strip()
                except Exception as e:
                    print(f"⚠️ Erreur détection pseudo existant: {e}")
            
            # Fallback vers la méthode de base
            return super()._detect_text_in_region(region_img, is_player_name)
            
        except Exception as e:
            print(f"❌ Erreur détection texte: {e}")
            return ""
    
    def get_enhanced_detection_info(self) -> Dict[str, Any]:
        """Obtient des informations sur les systèmes de détection disponibles"""
        return {
            "cuda_detector_available": self.existing_detector is not None,
            "multi_ocr_available": self.multi_ocr is not None,
            "base_detector_available": True,
            "detection_methods": {
                "cards": "CUDA + Multi-OCR + Base" if self.existing_detector and self.multi_ocr else "Base",
                "amounts": "CUDA + Base" if self.existing_detector else "Base",
                "player_names": "Multi-OCR + Base" if self.multi_ocr else "Base"
            }
        }

class RealtimePokerAdvisor:
    """Conseiller poker temps réel complet"""
    
    def __init__(self, config_path: str = None):
        # Utiliser le détecteur amélioré
        self.detector = EnhancedAutoDetector(config_path)
        
        # État du conseiller
        self.active = False
        self.last_advice = None
        self.advice_history = []
        
        print("🎯 Conseiller Poker Temps Réel initialisé")
    
    def start(self):
        """Démarre le conseiller temps réel"""
        try:
            self.detector.start_monitoring()
            self.active = True
            
            print("🚀 Conseiller temps réel démarré")
            print("🔄 Surveillance automatique des joueurs et actions...")
            
            # Afficher les infos de détection
            detection_info = self.detector.get_enhanced_detection_info()
            print(f"🔧 Systèmes de détection disponibles:")
            for method, system in detection_info["detection_methods"].items():
                print(f"  • {method}: {system}")
            
        except Exception as e:
            print(f"❌ Erreur démarrage conseiller: {e}")
            raise
    
    def stop(self):
        """Arrête le conseiller"""
        try:
            self.detector.stop_monitoring()
            self.active = False
            print("⏹️ Conseiller temps réel arrêté")
        except Exception as e:
            print(f"❌ Erreur arrêt conseiller: {e}")
    
    def get_realtime_advice(self) -> Dict[str, Any]:
        """Obtient un conseil temps réel complet"""
        try:
            if not self.active:
                return {"error": "Conseiller non actif"}
            
            # Obtenir le conseil du détecteur
            advice = self.detector.get_current_advice()
            
            # Enrichir avec des informations supplémentaires
            if "error" not in advice:
                # Ajouter l'historique des conseils
                advice["advice_history"] = self.advice_history[-5:]  # 5 derniers conseils
                
                # Ajouter des métriques de confiance
                advice["confidence_metrics"] = self._calculate_confidence_metrics()
                
                # Sauvegarder dans l'historique
                self.last_advice = advice
                self.advice_history.append({
                    "timestamp": advice.get("timestamp", "unknown"),
                    "action": advice.get("final_recommendation", "unknown"),
                    "players": len(advice.get("table_state", {}).get("players", []))
                })
                
                # Garder seulement les 20 derniers
                if len(self.advice_history) > 20:
                    self.advice_history = self.advice_history[-20:]
            
            return advice
            
        except Exception as e:
            print(f"❌ Erreur conseil temps réel: {e}")
            return {"error": str(e)}
    
    def _calculate_confidence_metrics(self) -> Dict[str, float]:
        """Calcule des métriques de confiance pour les conseils"""
        try:
            summary = self.detector.get_table_summary()
            
            # Confiance basée sur les données disponibles
            confidence = 0.0
            
            # Joueurs détectés (+20% par joueur, max 80%)
            player_confidence = min(len(summary.get("active_players", [])) * 0.2, 0.8)
            confidence += player_confidence
            
            # Cartes détectées (+10% par carte)
            cards_detected = len(summary.get("my_cards", [])) + len(summary.get("board_cards", []))
            card_confidence = min(cards_detected * 0.1, 0.5)
            confidence += card_confidence
            
            # Montants détectés (+15% si pot et jetons détectés)
            if summary.get("pot", 0) > 0:
                confidence += 0.1
            if summary.get("my_chips", 0) > 0:
                confidence += 0.1
            
            # Actions récentes (+5% par action récente, max 20%)
            recent_actions = summary.get("recent_actions_count", 0)
            action_confidence = min(recent_actions * 0.05, 0.2)
            confidence += action_confidence
            
            return {
                "overall": min(confidence, 1.0),
                "players": player_confidence,
                "cards": card_confidence,
                "amounts": 0.2 if summary.get("pot", 0) > 0 and summary.get("my_chips", 0) > 0 else 0.1,
                "actions": action_confidence
            }
            
        except Exception as e:
            print(f"❌ Erreur calcul confiance: {e}")
            return {"overall": 0.0}
    
    def force_full_update(self):
        """Force une mise à jour complète"""
        try:
            print("🔄 Mise à jour complète forcée...")
            
            # Scanner les joueurs
            self.detector.force_player_scan()
            
            # Mettre à jour la table
            self.detector.force_table_update()
            
            # Obtenir un nouveau conseil
            advice = self.get_realtime_advice()
            
            print("✅ Mise à jour complète terminée")
            return advice
            
        except Exception as e:
            print(f"❌ Erreur mise à jour complète: {e}")
            return {"error": str(e)}
    
    def get_status_report(self) -> Dict[str, Any]:
        """Obtient un rapport de status complet"""
        try:
            summary = self.detector.get_table_summary()
            detection_info = self.detector.get_enhanced_detection_info()
            confidence = self._calculate_confidence_metrics()
            
            return {
                "active": self.active,
                "monitoring": summary.get("monitoring", False),
                "table_summary": summary,
                "detection_systems": detection_info,
                "confidence_metrics": confidence,
                "advice_history_count": len(self.advice_history),
                "last_advice_available": self.last_advice is not None
            }
            
        except Exception as e:
            print(f"❌ Erreur rapport status: {e}")
            return {"error": str(e)}

# Fonctions utilitaires pour l'intégration
def create_realtime_advisor(config_path: str = None) -> RealtimePokerAdvisor:
    """Crée un conseiller temps réel complet"""
    return RealtimePokerAdvisor(config_path)

def start_enhanced_monitoring(config_path: str = None) -> RealtimePokerAdvisor:
    """Démarre la surveillance améliorée"""
    advisor = create_realtime_advisor(config_path)
    advisor.start()
    return advisor

if __name__ == "__main__":
    # Test du système intégré
    print("🎯 Test du Système Intégré de Détection")
    print("=" * 50)
    
    try:
        # Créer le conseiller
        advisor = create_realtime_advisor()
        
        # Afficher le rapport de status
        status = advisor.get_status_report()
        print(f"📊 Status:")
        print(f"  • Systèmes de détection: {status['detection_systems']['detection_methods']}")
        print(f"  • CUDA disponible: {status['detection_systems']['cuda_detector_available']}")
        print(f"  • Multi-OCR disponible: {status['detection_systems']['multi_ocr_available']}")
        
        # Démarrer la surveillance
        advisor.start()
        
        print("\n✅ Système intégré démarré avec succès")
        print("🔄 Surveillance en cours avec tous les systèmes disponibles...")
        print("💡 Appuyez sur Ctrl+C pour arrêter")
        
        # Boucle de test
        import time
        try:
            while True:
                time.sleep(10)
                
                # Obtenir un conseil
                advice = advisor.get_realtime_advice()
                if "error" not in advice:
                    confidence = advice.get("confidence_metrics", {})
                    print(f"\n📊 Confiance globale: {confidence.get('overall', 0)*100:.1f}%")
                
        except KeyboardInterrupt:
            print("\n⏹️ Arrêt demandé")
        
        # Arrêter
        advisor.stop()
        print("✅ Test terminé")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
